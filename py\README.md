# Git提交记录批量生成工具

## 功能说明

这个Python脚本用于批量生成Git提交记录，模拟真实的开发场景。主要功能包括：

1. **远程服务器时间同步**: 使用paramiko连接远程服务器，修改系统时间到2020-2023年工作时间
2. **代码模块分配**: 将项目代码模块分配给4个开发者
3. **批量提交**: 每个开发者生成100个提交记录
4. **真实模拟**: 通过添加/删除空行的方式修改文件，使用真实的提交信息

## 开发者分配

- **lijiangrong**: ly_server/src/common/ 目录下的基础工具类
- **qinxiaojing**: ly_server/src/common/ 目录下的字符串和内存处理类  
- **liuxukai**: ly_server/src/lib/ 目录下的配置管理类
- **lisiqi**: ly_server/src/server/ 目录下的服务器核心功能

## 使用方法

### 1. 安装依赖

```bash
pip install paramiko pandas numpy
```

### 2. 配置远程服务器

运行脚本时会提示输入：
- 远程服务器IP地址
- root用户密码

### 3. 运行脚本

```bash
cd py
python git.py
```

### 4. 执行流程

1. 输入远程服务器信息
2. 确认开始执行
3. 脚本会自动：
   - 保存模块分配到 `module_assignment.json`
   - 为每个用户生成100个提交
   - 每次提交前同步远程服务器时间
   - 随机修改分配的文件（添加/删除空行）
   - 使用真实的提交信息

## 注意事项

1. **确保在Git仓库中运行**: 脚本会检查当前目录是否为Git仓库
2. **远程服务器权限**: 需要root权限来修改系统时间
3. **网络连接**: 确保能够SSH连接到远程服务器
4. **备份代码**: 建议在测试环境中运行，避免影响生产代码
5. **时间范围**: 生成的提交时间为2020-2023年工作日9:00-18:00

## 输出文件

- `module_assignment.json`: 代码模块分配记录
- Git提交历史: 400个提交记录（4个用户 × 100个提交）

## 提交信息示例

脚本使用真实的提交信息模板：
- "fix: 修复内存泄漏问题"
- "feat: 添加新的配置参数"  
- "refactor: 重构代码结构"
- "perf: 优化查询性能"
- 等等...

## 安全提醒

- 请在测试环境中使用
- 确保远程服务器的安全性
- 不要在生产环境中修改系统时间
