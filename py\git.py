# 用于批量生成git提交记录的脚本
import pandas as pd
import numpy as np
import paramiko
import os
import json
import random
import datetime
import subprocess
import time
from pathlib import Path

class GitCommitGenerator:
    def __init__(self):
        # 远程服务器配置
        self.remote_host = ""  # 需要配置远程服务器IP
        self.remote_user = "root"
        self.remote_password = ""  # 需要配置密码

        # Git用户配置
        self.git_users = [
            {"name": "lijiangrong", "email": "<EMAIL>"},
            {"name": "qinxiaojing", "email": "<EMAIL>"},
            {"name": "liuxukai", "email": "<EMAIL>"},
            {"name": "lisiqi", "email": "<EMAIL>"}
        ]

        # 代码模块分配
        self.module_assignment = {
            "lijiangrong": [
                "ly_server/src/common/asset.cpp", "ly_server/src/common/asset.h",
                "ly_server/src/common/cJSON.cpp", "ly_server/src/common/cJSON.h",
                "ly_server/src/common/config.cpp", "ly_server/src/common/config.h",
                "ly_server/src/common/datetime.cpp", "ly_server/src/common/datetime.h",
                "ly_server/src/common/file.cpp", "ly_server/src/common/file.h",
                "ly_server/src/common/http.cpp", "ly_server/src/common/http.h",
                "ly_server/src/common/log.cpp", "ly_server/src/common/log.h",
                "ly_server/src/common/md5.cpp", "ly_server/src/common/md5.h"
            ],
            "qinxiaojing": [
                "ly_server/src/common/ip.cpp", "ly_server/src/common/ip.h",
                "ly_server/src/common/strings.cpp", "ly_server/src/common/strings.h",
                "ly_server/src/common/stringutil.cpp", "ly_server/src/common/stringutil.h",
                "ly_server/src/common/sha256.cpp", "ly_server/src/common/sha256.h",
                "ly_server/src/common/slice.cpp", "ly_server/src/common/slice.h",
                "ly_server/src/common/tic.cpp", "ly_server/src/common/tic.h",
                "ly_server/src/common/mmapped_file.cpp", "ly_server/src/common/mmapped_file.h",
                "ly_server/src/common/scoped_mmap.cpp", "ly_server/src/common/scoped_mmap.h"
            ],
            "liuxukai": [
                "ly_server/src/lib/config_agent.cpp", "ly_server/src/lib/config_agent.h",
                "ly_server/src/lib/config_bwlist.cpp", "ly_server/src/lib/config_bwlist.h",
                "ly_server/src/lib/config_class.cpp", "ly_server/src/lib/config_class.h",
                "ly_server/src/lib/config_event.cpp", "ly_server/src/lib/config_event.h",
                "ly_server/src/lib/config_internalip.cpp", "ly_server/src/lib/config_internalip.h",
                "ly_server/src/lib/config_internalsrv.cpp", "ly_server/src/lib/config_internalsrv.h",
                "ly_server/src/lib/config_mo.cpp", "ly_server/src/lib/config_mo.h",
                "ly_server/src/lib/config_user.cpp", "ly_server/src/lib/config_user.h"
            ],
            "lisiqi": [
                "ly_server/src/server/auth.cpp", "ly_server/src/server/bwlist.cpp",
                "ly_server/src/server/config.cpp", "ly_server/src/server/config_pusher.cpp",
                "ly_server/src/server/dbc.cpp", "ly_server/src/server/dbc.h",
                "ly_server/src/server/event.cpp", "ly_server/src/server/event_feature.cpp",
                "ly_server/src/server/evidence.cpp", "ly_server/src/server/feature.cpp",
                "ly_server/src/server/gen_event.cpp", "ly_server/src/server/geoinfo.cpp",
                "ly_server/src/server/internalip.cpp", "ly_server/src/server/ipinfo.cpp",
                "ly_server/src/server/locinfo.cpp", "ly_server/src/server/mo.cpp"
            ]
        }

        # 真实的提交信息模板
        self.commit_messages = [
            "fix: 修复内存泄漏问题",
            "feat: 添加新的配置参数",
            "refactor: 重构代码结构",
            "fix: 修复空指针异常",
            "perf: 优化查询性能",
            "docs: 更新注释文档",
            "style: 代码格式化",
            "fix: 修复边界条件处理",
            "feat: 增加错误处理机制",
            "refactor: 简化函数逻辑",
            "fix: 修复并发访问问题",
            "perf: 减少内存占用",
            "feat: 添加日志输出",
            "fix: 修复配置读取错误",
            "refactor: 抽取公共方法",
            "fix: 修复字符串处理bug",
            "feat: 增加参数验证",
            "perf: 优化算法效率",
            "fix: 修复资源释放问题",
            "refactor: 更新接口定义"
        ]

    def save_module_assignment(self):
        """保存模块分配到JSON文件"""
        with open('module_assignment.json', 'w', encoding='utf-8') as f:
            json.dump(self.module_assignment, f, ensure_ascii=False, indent=2)
        print("模块分配已保存到 module_assignment.json")

    def connect_remote_server(self):
        """连接远程服务器"""
        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh_client.connect(
                hostname=self.remote_host,
                username=self.remote_user,
                password=self.remote_password
            )
            print(f"成功连接到远程服务器 {self.remote_host}")
            return True
        except Exception as e:
            print(f"连接远程服务器失败: {e}")
            return False

    def generate_random_work_time(self):
        """生成2020-2023年间工作时间的随机时间点"""
        start_year = 2020
        end_year = 2023

        # 随机选择年份
        year = random.randint(start_year, end_year)

        # 随机选择月份
        month = random.randint(1, 12)

        # 根据月份确定天数
        if month in [1, 3, 5, 7, 8, 10, 12]:
            max_day = 31
        elif month in [4, 6, 9, 11]:
            max_day = 30
        else:  # 2月
            if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0):
                max_day = 29
            else:
                max_day = 28

        day = random.randint(1, max_day)

        # 检查是否为工作日（周一到周五）
        date_obj = datetime.date(year, month, day)
        if date_obj.weekday() >= 5:  # 周六或周日
            return self.generate_random_work_time()  # 递归重新生成

        # 工作时间 9:00-18:00
        hour = random.randint(9, 17)
        minute = random.randint(0, 59)
        second = random.randint(0, 59)

        return datetime.datetime(year, month, day, hour, minute, second)

    def set_remote_time(self, target_time):
        """设置远程服务器时间"""
        try:
            time_str = target_time.strftime("%Y%m%d%H%M%S")
            command = f"date {time_str}"

            stdin, stdout, stderr = self.ssh_client.exec_command(command)
            result = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')

            if error:
                print(f"设置远程时间失败: {error}")
                return False

            print(f"远程服务器时间已设置为: {target_time}")
            return True
        except Exception as e:
            print(f"设置远程时间异常: {e}")
            return False

    def get_remote_time(self):
        """获取远程服务器时间"""
        try:
            stdin, stdout, stderr = self.ssh_client.exec_command("date")
            result = stdout.read().decode('utf-8').strip()
            print(f"远程服务器当前时间: {result}")
            return result
        except Exception as e:
            print(f"获取远程时间失败: {e}")
            return None

    def modify_file_with_empty_lines(self, file_path, action='add'):
        """通过添加或删除空行来修改文件"""
        try:
            if not os.path.exists(file_path):
                print(f"文件不存在: {file_path}")
                return False

            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()

            if action == 'add':
                # 随机位置添加1-3个空行
                insert_pos = random.randint(0, len(lines))
                empty_lines_count = random.randint(1, 3)
                for i in range(empty_lines_count):
                    lines.insert(insert_pos, '\n')
                print(f"在 {file_path} 第{insert_pos}行添加了{empty_lines_count}个空行")

            elif action == 'remove':
                # 删除随机的空行
                empty_line_indices = [i for i, line in enumerate(lines) if line.strip() == '']
                if empty_line_indices:
                    remove_count = min(random.randint(1, 2), len(empty_line_indices))
                    indices_to_remove = random.sample(empty_line_indices, remove_count)
                    # 从后往前删除，避免索引变化
                    for idx in sorted(indices_to_remove, reverse=True):
                        lines.pop(idx)
                    print(f"从 {file_path} 删除了{remove_count}个空行")
                else:
                    print(f"{file_path} 没有空行可删除，改为添加空行")
                    return self.modify_file_with_empty_lines(file_path, 'add')

            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)

            return True
        except Exception as e:
            print(f"修改文件失败 {file_path}: {e}")
            return False

    def git_commit(self, user, message, commit_time):
        """执行git提交"""
        try:
            # 设置git用户
            subprocess.run(['git', 'config', 'user.name', user['name']], check=True)
            subprocess.run(['git', 'config', 'user.email', user['email']], check=True)

            # 添加所有修改的文件
            subprocess.run(['git', 'add', '.'], check=True)

            # 设置提交时间环境变量
            env = os.environ.copy()
            time_str = commit_time.strftime("%Y-%m-%d %H:%M:%S")
            env['GIT_AUTHOR_DATE'] = time_str
            env['GIT_COMMITTER_DATE'] = time_str

            # 提交
            subprocess.run(['git', 'commit', '-m', message], env=env, check=True)

            print(f"用户 {user['name']} 在 {time_str} 提交: {message}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"Git提交失败: {e}")
            return False
        except Exception as e:
            print(f"Git提交异常: {e}")
            return False

    def generate_commits_for_user(self, user_index, commit_count=100):
        """为指定用户生成提交记录"""
        user = self.git_users[user_index]
        user_files = self.module_assignment[user['name']]

        print(f"\n开始为用户 {user['name']} 生成 {commit_count} 个提交...")

        successful_commits = 0
        for i in range(commit_count):
            try:
                # 生成随机时间
                commit_time = self.generate_random_work_time()

                # 连接远程服务器并设置时间
                if not self.connect_remote_server():
                    print("无法连接远程服务器，跳过此次提交")
                    continue

                if not self.set_remote_time(commit_time):
                    print("无法设置远程时间，跳过此次提交")
                    self.ssh_client.close()
                    continue

                # 随机选择要修改的文件
                file_to_modify = random.choice(user_files)

                # 随机选择操作（添加或删除空行）
                action = random.choice(['add', 'remove'])

                # 修改文件
                if self.modify_file_with_empty_lines(file_to_modify, action):
                    # 随机选择提交信息
                    commit_message = random.choice(self.commit_messages)

                    # 执行git提交
                    if self.git_commit(user, commit_message, commit_time):
                        successful_commits += 1
                        print(f"进度: {successful_commits}/{commit_count}")

                    # 短暂延迟
                    time.sleep(0.5)

                self.ssh_client.close()

            except Exception as e:
                print(f"生成提交时发生异常: {e}")
                continue

        print(f"用户 {user['name']} 成功生成 {successful_commits} 个提交")
        return successful_commits

    def run_batch_commits(self):
        """批量生成所有用户的提交记录"""
        print("开始批量生成Git提交记录...")

        # 保存模块分配
        self.save_module_assignment()

        # 检查是否在git仓库中
        try:
            subprocess.run(['git', 'status'], check=True, capture_output=True)
        except subprocess.CalledProcessError:
            print("当前目录不是git仓库，请先初始化git仓库")
            return False

        total_commits = 0

        # 为每个用户生成提交
        for i, user in enumerate(self.git_users):
            print(f"\n{'='*50}")
            print(f"处理用户 {i+1}/4: {user['name']}")
            print(f"{'='*50}")

            commits = self.generate_commits_for_user(i, 100)
            total_commits += commits

        print(f"\n{'='*50}")
        print(f"批量提交完成！")
        print(f"总共生成了 {total_commits} 个提交")
        print(f"模块分配信息已保存到 module_assignment.json")
        print(f"{'='*50}")

        return True

    def configure_remote_server(self, host, password):
        """配置远程服务器信息"""
        self.remote_host = host
        self.remote_password = password
        print(f"远程服务器配置完成: {host}")

def main():
    """主函数"""
    print("Git提交记录批量生成工具")
    print("="*50)

    generator = GitCommitGenerator()

    # 配置远程服务器（需要用户输入）
    print("请配置远程服务器信息:")
    remote_host = input("远程服务器IP地址: ").strip()
    remote_password = input("root用户密码: ").strip()

    if not remote_host or not remote_password:
        print("远程服务器信息不完整，程序退出")
        return

    generator.configure_remote_server(remote_host, remote_password)

    # 确认开始执行
    confirm = input("\n确认开始生成提交记录？(y/N): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return

    # 执行批量提交
    generator.run_batch_commits()

if __name__ == "__main__":
    main()

