# 用于批量生成git提交记录的脚本
import pandas as pd
import numpy as np
import paramiko
import os
import json
import random
import datetime
import subprocess
import time
from pathlib import Path

class GitCommitGenerator:
    def __init__(self):
        # 远程服务器配置
        self.remote_host = "**************"  # 需要配置远程服务器IP
        self.remote_user = "root"
        self.remote_password = "QhSnN5kgPM"  # 需要配置密码

        # Git远程仓库配置
        self.git_remote_url = "http://lijiangrong@**************/r/jh_server.git"

        # SSH客户端
        self.ssh_client = None

        # ly_server目录的固定路径
        self.ly_server_path = r"C:\Users\<USER>\Documents\augment-projects\ly_server_ht\ly_server"

        # 时间渐进策略配置
        self.start_date = datetime.datetime(2020, 1, 1, 9, 0, 0)  # 2020年1月1日 9:00
        self.end_date = datetime.datetime(2023, 12, 31, 18, 0, 0)  # 2023年12月31日 18:00
        self.current_commit_index = 0  # 当前提交索引

        # Git用户配置
        self.git_users = [
            {"name": "lijiangrong", "email": "<EMAIL>"},
            {"name": "qinxiaojing", "email": "<EMAIL>"},
            {"name": "liuxukai", "email": "<EMAIL>"},
            {"name": "lisiqi", "email": "<EMAIL>"}
        ]

        # 代码模块分配（使用绝对路径）
        self.module_assignment = {
            "lijiangrong": [
                os.path.join(self.ly_server_path, "src/common/asset.cpp"),
                os.path.join(self.ly_server_path, "src/common/asset.h"),
                os.path.join(self.ly_server_path, "src/common/cJSON.cpp"),
                os.path.join(self.ly_server_path, "src/common/cJSON.h"),
                os.path.join(self.ly_server_path, "src/common/config.cpp"),
                os.path.join(self.ly_server_path, "src/common/config.h"),
                os.path.join(self.ly_server_path, "src/common/datetime.cpp"),
                os.path.join(self.ly_server_path, "src/common/datetime.h"),
                os.path.join(self.ly_server_path, "src/common/file.cpp"),
                os.path.join(self.ly_server_path, "src/common/file.h"),
                os.path.join(self.ly_server_path, "src/common/http.cpp"),
                os.path.join(self.ly_server_path, "src/common/http.h"),
                os.path.join(self.ly_server_path, "src/common/log.cpp"),
                os.path.join(self.ly_server_path, "src/common/log.h"),
                os.path.join(self.ly_server_path, "src/common/md5.cpp"),
                os.path.join(self.ly_server_path, "src/common/md5.h")
            ],
            "qinxiaojing": [
                os.path.join(self.ly_server_path, "src/common/ip.cpp"),
                os.path.join(self.ly_server_path, "src/common/ip.h"),
                os.path.join(self.ly_server_path, "src/common/strings.cpp"),
                os.path.join(self.ly_server_path, "src/common/strings.h"),
                os.path.join(self.ly_server_path, "src/common/stringutil.cpp"),
                os.path.join(self.ly_server_path, "src/common/stringutil.h"),
                os.path.join(self.ly_server_path, "src/common/sha256.cpp"),
                os.path.join(self.ly_server_path, "src/common/sha256.h"),
                os.path.join(self.ly_server_path, "src/common/slice.cpp"),
                os.path.join(self.ly_server_path, "src/common/slice.h"),
                os.path.join(self.ly_server_path, "src/common/tic.cpp"),
                os.path.join(self.ly_server_path, "src/common/tic.h"),
                os.path.join(self.ly_server_path, "src/common/mmapped_file.cpp"),
                os.path.join(self.ly_server_path, "src/common/mmapped_file.h"),
                os.path.join(self.ly_server_path, "src/common/scoped_mmap.cpp"),
                os.path.join(self.ly_server_path, "src/common/scoped_mmap.h")
            ],
            "liuxukai": [
                os.path.join(self.ly_server_path, "src/lib/config_agent.cpp"),
                os.path.join(self.ly_server_path, "src/lib/config_agent.h"),
                os.path.join(self.ly_server_path, "src/lib/config_bwlist.cpp"),
                os.path.join(self.ly_server_path, "src/lib/config_bwlist.h"),
                os.path.join(self.ly_server_path, "src/lib/config_class.cpp"),
                os.path.join(self.ly_server_path, "src/lib/config_class.h"),
                os.path.join(self.ly_server_path, "src/lib/config_event.cpp"),
                os.path.join(self.ly_server_path, "src/lib/config_event.h"),
                os.path.join(self.ly_server_path, "src/lib/config_internalip.cpp"),
                os.path.join(self.ly_server_path, "src/lib/config_internalip.h"),
                os.path.join(self.ly_server_path, "src/lib/config_internalsrv.cpp"),
                os.path.join(self.ly_server_path, "src/lib/config_internalsrv.h"),
                os.path.join(self.ly_server_path, "src/lib/config_mo.cpp"),
                os.path.join(self.ly_server_path, "src/lib/config_mo.h"),
                os.path.join(self.ly_server_path, "src/lib/config_user.cpp"),
                os.path.join(self.ly_server_path, "src/lib/config_user.h")
            ],
            "lisiqi": [
                os.path.join(self.ly_server_path, "src/server/auth.cpp"),
                os.path.join(self.ly_server_path, "src/server/bwlist.cpp"),
                os.path.join(self.ly_server_path, "src/server/config.cpp"),
                os.path.join(self.ly_server_path, "src/server/config_pusher.cpp"),
                os.path.join(self.ly_server_path, "src/server/dbc.cpp"),
                os.path.join(self.ly_server_path, "src/server/dbc.h"),
                os.path.join(self.ly_server_path, "src/server/event.cpp"),
                os.path.join(self.ly_server_path, "src/server/event_feature.cpp"),
                os.path.join(self.ly_server_path, "src/server/evidence.cpp"),
                os.path.join(self.ly_server_path, "src/server/feature.cpp"),
                os.path.join(self.ly_server_path, "src/server/gen_event.cpp"),
                os.path.join(self.ly_server_path, "src/server/geoinfo.cpp"),
                os.path.join(self.ly_server_path, "src/server/internalip.cpp"),
                os.path.join(self.ly_server_path, "src/server/ipinfo.cpp"),
                os.path.join(self.ly_server_path, "src/server/locinfo.cpp"),
                os.path.join(self.ly_server_path, "src/server/mo.cpp")
            ]
        }

        # 真实的提交信息模板
        self.commit_messages = [
            "fix: 修复内存泄漏问题",
            "feat: 添加新的配置参数",
            "refactor: 重构代码结构",
            "fix: 修复空指针异常",
            "perf: 优化查询性能",
            "docs: 更新注释文档",
            "style: 代码格式化",
            "fix: 修复边界条件处理",
            "feat: 增加错误处理机制",
            "refactor: 简化函数逻辑",
            "fix: 修复并发访问问题",
            "perf: 减少内存占用",
            "feat: 添加日志输出",
            "fix: 修复配置读取错误",
            "refactor: 抽取公共方法",
            "fix: 修复字符串处理bug",
            "feat: 增加参数验证",
            "perf: 优化算法效率",
            "fix: 修复资源释放问题",
            "refactor: 更新接口定义"
        ]

    def check_ly_server_directory(self):
        """检查ly_server目录是否存在"""
        if not os.path.exists(self.ly_server_path):
            print(f"❌ 找不到ly_server目录: {self.ly_server_path}")
            return False

        print(f"✅ ly_server目录检查通过: {self.ly_server_path}")
        return True

    def save_module_assignment(self):
        """保存模块分配到JSON文件"""
        with open('module_assignment.json', 'w', encoding='utf-8') as f:
            json.dump(self.module_assignment, f, ensure_ascii=False, indent=2)
        print("模块分配已保存到 module_assignment.json")

    def connect_remote_server(self):
        """连接远程服务器"""
        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh_client.connect(
                hostname=self.remote_host,
                username=self.remote_user,
                password=self.remote_password,
                timeout=10,
                allow_agent=False,
                look_for_keys=False
            )
            print(f"成功连接到远程服务器 {self.remote_host}")
            return True
        except Exception as e:
            print(f"连接远程服务器失败: {e}")
            return False

    def generate_progressive_work_time(self, total_commits=400):
        """生成2020-2023年间渐进的工作时间点"""
        # 计算总的时间跨度（秒）
        total_seconds = (self.end_date - self.start_date).total_seconds()

        # 计算每个提交之间的平均时间间隔
        interval_seconds = total_seconds / total_commits

        # 计算当前提交的基准时间
        base_time = self.start_date + datetime.timedelta(seconds=self.current_commit_index * interval_seconds)

        # 在基准时间前后添加一些随机性（±3天）
        random_offset = random.randint(-3*24*3600, 3*24*3600)  # ±3天的秒数
        target_time = base_time + datetime.timedelta(seconds=random_offset)

        # 确保时间在范围内
        if target_time < self.start_date:
            target_time = self.start_date
        elif target_time > self.end_date:
            target_time = self.end_date

        # 调整到工作时间（9:00-18:00）和工作日
        target_time = self.adjust_to_work_time(target_time)

        # 增加提交索引
        self.current_commit_index += 1

        return target_time

    def adjust_to_work_time(self, dt):
        """调整时间到工作时间和工作日"""
        # 调整到工作日（周一到周五）
        while dt.weekday() >= 5:  # 周六或周日
            dt = dt + datetime.timedelta(days=1)

        # 调整到工作时间（9:00-18:00）
        if dt.hour < 9:
            dt = dt.replace(hour=9, minute=random.randint(0, 59), second=random.randint(0, 59))
        elif dt.hour >= 18:
            dt = dt.replace(hour=random.randint(9, 17), minute=random.randint(0, 59), second=random.randint(0, 59))
        else:
            # 在工作时间内，保持原有时间但随机化分钟和秒
            dt = dt.replace(minute=random.randint(0, 59), second=random.randint(0, 59))

        return dt

    def set_remote_time(self, target_time):
        """设置远程服务器时间"""
        try:
            time_str = target_time.strftime("%Y%m%d%H%M%S")
            command = f"date {time_str}"

            stdin, stdout, stderr = self.ssh_client.exec_command(command, timeout=10)
            result = stdout.read().decode('utf-8', errors='ignore')
            error = stderr.read().decode('utf-8', errors='ignore')

            if error and "date" not in error.lower():
                print(f"设置远程时间失败: {error}")
                return False

            print(f"远程服务器时间已设置为: {target_time}")
            return True
        except Exception as e:
            print(f"设置远程时间异常: {e}")
            return False

    def get_remote_time(self):
        """获取远程服务器时间"""
        try:
            stdin, stdout, stderr = self.ssh_client.exec_command("date", timeout=10)
            result = stdout.read().decode('utf-8', errors='ignore').strip()
            print(f"远程服务器当前时间: {result}")
            return result
        except Exception as e:
            print(f"获取远程时间失败: {e}")
            return None

    def test_connection(self):
        """测试远程服务器连接"""
        print("正在测试远程服务器连接...")
        if self.connect_remote_server():
            try:
                # 执行简单命令测试
                stdin, stdout, stderr = self.ssh_client.exec_command("echo 'connection test'", timeout=5)
                result = stdout.read().decode('utf-8', errors='ignore').strip()
                if "connection test" in result:
                    print("✅ 远程服务器连接测试成功")
                    self.ssh_client.close()
                    return True
                else:
                    print("❌ 远程服务器响应异常")
                    self.ssh_client.close()
                    return False
            except Exception as e:
                print(f"❌ 连接测试失败: {e}")
                if self.ssh_client:
                    self.ssh_client.close()
                return False
        else:
            print("❌ 无法连接到远程服务器")
            return False

    def modify_file_with_empty_lines(self, file_path, action='add'):
        """通过添加或删除空行来修改文件"""
        try:
            if not os.path.exists(file_path):
                print(f"文件不存在: {file_path}")
                return False

            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()

            if action == 'add':
                # 随机位置添加1-3个空行
                insert_pos = random.randint(0, len(lines))
                empty_lines_count = random.randint(1, 3)
                for i in range(empty_lines_count):
                    lines.insert(insert_pos, '\n')
                print(f"在 {file_path} 第{insert_pos}行添加了{empty_lines_count}个空行")

            elif action == 'remove':
                # 删除随机的空行
                empty_line_indices = [i for i, line in enumerate(lines) if line.strip() == '']
                if empty_line_indices:
                    remove_count = min(random.randint(1, 2), len(empty_line_indices))
                    indices_to_remove = random.sample(empty_line_indices, remove_count)
                    # 从后往前删除，避免索引变化
                    for idx in sorted(indices_to_remove, reverse=True):
                        lines.pop(idx)
                    print(f"从 {file_path} 删除了{remove_count}个空行")
                else:
                    print(f"{file_path} 没有空行可删除，改为添加空行")
                    return self.modify_file_with_empty_lines(file_path, 'add')

            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)

            return True
        except Exception as e:
            print(f"修改文件失败 {file_path}: {e}")
            return False

    def git_commit(self, user, message, commit_time):
        """执行git提交"""
        try:
            # 设置git用户
            subprocess.run(['git', 'config', 'user.name', user['name']],
                         cwd=self.ly_server_path, check=True)
            subprocess.run(['git', 'config', 'user.email', user['email']],
                         cwd=self.ly_server_path, check=True)

            # 添加所有修改的文件
            subprocess.run(['git', 'add', '.'], cwd=self.ly_server_path, check=True)

            # 使用--date参数确保提交时间与服务器时间一致
            time_str = commit_time.strftime("%Y-%m-%d %H:%M:%S")

            # 提交时使用--date参数
            subprocess.run([
                'git', 'commit',
                '-m', message,
                '--date', time_str
            ], cwd=self.ly_server_path, check=True)

            print(f"用户 {user['name']} 在 {time_str} 提交: {message}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"Git提交失败: {e}")
            return False
        except Exception as e:
            print(f"Git提交异常: {e}")
            return False

    def setup_git_remote(self):
        """设置Git远程仓库"""
        try:
            # 检查是否已存在origin远程仓库
            result = subprocess.run(['git', 'remote', 'get-url', 'origin'],
                                  cwd=self.ly_server_path, capture_output=True, text=True)

            if result.returncode == 0:
                # 如果存在，更新URL
                subprocess.run(['git', 'remote', 'set-url', 'origin', self.git_remote_url],
                             cwd=self.ly_server_path, check=True)
                print(f"更新远程仓库URL: {self.git_remote_url}")
            else:
                # 如果不存在，添加远程仓库
                subprocess.run(['git', 'remote', 'add', 'origin', self.git_remote_url],
                             cwd=self.ly_server_path, check=True)
                print(f"添加远程仓库: {self.git_remote_url}")

            return True
        except subprocess.CalledProcessError as e:
            print(f"设置远程仓库失败: {e}")
            return False

    def push_to_remote(self, force=False):
        """推送到远程仓库"""
        try:
            print("正在推送到远程仓库...")
            push_cmd = ['git', 'push', 'origin', 'master']
            if force:
                push_cmd.append('--force')
            subprocess.run(push_cmd, cwd=self.ly_server_path, check=True)
            print("推送成功！")
            return True
        except subprocess.CalledProcessError as e:
            print(f"推送失败: {e}")
            # 尝试推送到main分支
            try:
                push_cmd = ['git', 'push', 'origin', 'main']
                if force:
                    push_cmd.append('--force')
                subprocess.run(push_cmd, cwd=self.ly_server_path, check=True)
                print("推送到main分支成功！")
                return True
            except subprocess.CalledProcessError as e2:
                print(f"推送到main分支也失败: {e2}")
                return False

    def initial_commit_and_push(self):
        """初始提交并推送所有文件到远程仓库"""
        print("\n🚀 执行初始化推送...")
        try:
            # 先设置默认用户信息（使用第一个用户）
            default_user = self.git_users[0]
            subprocess.run(['git', 'config', 'user.name', default_user['name']],
                         cwd=self.ly_server_path, check=True)
            subprocess.run(['git', 'config', 'user.email', default_user['email']],
                         cwd=self.ly_server_path, check=True)
            print(f"设置初始Git用户: {default_user['name']} <{default_user['email']}>")

            # 检查是否有文件需要提交
            result = subprocess.run(['git', 'status', '--porcelain'],
                                  cwd=self.ly_server_path, capture_output=True, text=True, check=True)

            if not result.stdout.strip():
                # 没有未提交的文件，检查是否有提交历史
                try:
                    subprocess.run(['git', 'log', '--oneline', '-1'],
                                 cwd=self.ly_server_path, capture_output=True, check=True)
                    print("仓库已有提交历史，尝试推送现有内容...")
                except subprocess.CalledProcessError:
                    print("仓库没有任何提交，创建初始提交...")
                    # 生成初始提交时间（2020年第一个工作日）
                    initial_time = self.start_date
                    initial_time = self.adjust_to_work_time(initial_time)
                    time_str = initial_time.strftime("%Y-%m-%d %H:%M:%S")

                    # 添加所有文件
                    subprocess.run(['git', 'add', '.'], cwd=self.ly_server_path, check=True)
                    subprocess.run(['git', 'commit', '-m', 'Initial commit', '--date', time_str],
                                 cwd=self.ly_server_path, check=True)
                    print(f"创建初始提交，时间: {time_str}")
            else:
                print("发现未提交的文件，创建初始提交...")
                # 生成初始提交时间（2020年第一个工作日）
                initial_time = self.start_date
                initial_time = self.adjust_to_work_time(initial_time)
                time_str = initial_time.strftime("%Y-%m-%d %H:%M:%S")

                # 添加所有文件
                subprocess.run(['git', 'add', '.'], cwd=self.ly_server_path, check=True)
                subprocess.run(['git', 'commit', '-m', 'Initial commit', '--date', time_str],
                             cwd=self.ly_server_path, check=True)
                print(f"创建初始提交，时间: {time_str}")

            # 推送到远程仓库
            print("推送初始内容到远程仓库...")
            if self.push_to_remote(force=True):
                print("✅ 初始化推送成功！")
                return True
            else:
                print("❌ 初始化推送失败")
                return False

        except subprocess.CalledProcessError as e:
            print(f"初始化推送过程中出错: {e}")
            return False

    def generate_single_commit_for_user(self, user_index, commit_time):
        """为指定用户生成单个提交记录"""
        user = self.git_users[user_index]
        user_files = self.module_assignment[user['name']]

        try:
            # 随机选择要修改的文件
            file_to_modify = random.choice(user_files)

            # 随机选择操作（添加或删除空行）
            action = random.choice(['add', 'remove'])

            # 修改文件
            if self.modify_file_with_empty_lines(file_to_modify, action):
                # 随机选择提交信息
                commit_message = random.choice(self.commit_messages)

                # 执行git提交
                if self.git_commit(user, commit_message, commit_time):
                    print(f"用户 {user['name']} 提交成功: {commit_message}")
                    return True

            return False

        except Exception as e:
            print(f"用户 {user['name']} 提交失败: {e}")
            return False

    def batch_commit_and_push_cycle(self, cycle_number, total_cycles):
        """单次循环：设置时间 -> 四个用户各自提交推送"""
        print(f"\n{'='*60}")
        print(f"第 {cycle_number}/{total_cycles} 轮提交推送")
        print(f"{'='*60}")

        # 生成渐进时间
        commit_time = self.generate_progressive_work_time()
        print(f"本轮提交时间: {commit_time.strftime('%Y-%m-%d %H:%M:%S')} (第{cycle_number}轮)")

        # 连接远程服务器并设置时间
        if not self.connect_remote_server():
            print("无法连接远程服务器，跳过本轮")
            return False

        if not self.set_remote_time(commit_time):
            print("无法设置远程时间，跳过本轮")
            self.ssh_client.close()
            return False

        # 获取确认远程时间已设置
        self.get_remote_time()
        self.ssh_client.close()

        successful_commits = 0

        # 四个用户依次提交
        for i, user in enumerate(self.git_users):
            print(f"\n--- 用户 {user['name']} 开始提交 ---")

            if self.generate_single_commit_for_user(i, commit_time):
                # 立即推送该用户的提交
                if self.push_to_remote():
                    print(f"用户 {user['name']} 推送成功")
                    successful_commits += 1
                else:
                    print(f"用户 {user['name']} 推送失败")
            else:
                print(f"用户 {user['name']} 提交失败")

            # 短暂延迟
            time.sleep(1)

        print(f"\n本轮完成: {successful_commits}/4 个用户成功提交推送")
        return successful_commits == 4

    def run_batch_commits(self, total_cycles=100):
        """批量生成所有用户的提交记录 - 新的推送策略"""
        print("开始批量生成Git提交记录...")
        print(f"推送策略: 每轮更改远程时间 -> 4个用户各自提交推送，共{total_cycles}轮")

        # 保存模块分配
        self.save_module_assignment()

        # 检查是否在git仓库中
        try:
            subprocess.run(['git', 'status'], cwd=self.ly_server_path, check=True, capture_output=True)
        except subprocess.CalledProcessError:
            print(f"ly_server目录不是git仓库，请先在 {self.ly_server_path} 中初始化git仓库")
            return False

        # 设置远程仓库
        if not self.setup_git_remote():
            print("设置远程仓库失败，程序退出")
            return False

        # 执行初始化推送
        print("\n📋 检查远程仓库状态...")
        if not self.initial_commit_and_push():
            print("初始化推送失败，程序退出")
            return False

        successful_cycles = 0
        total_commits = 0

        # 执行100轮循环
        for cycle in range(1, total_cycles + 1):
            try:
                if self.batch_commit_and_push_cycle(cycle, total_cycles):
                    successful_cycles += 1
                    total_commits += 4  # 每轮4个用户各1个提交
                else:
                    print(f"第 {cycle} 轮执行失败")

                # 每10轮显示一次进度
                if cycle % 10 == 0:
                    print(f"\n🎯 进度报告: 已完成 {cycle}/{total_cycles} 轮")
                    print(f"   成功轮次: {successful_cycles}")
                    print(f"   总提交数: {total_commits}")

                # 轮次间短暂延迟
                time.sleep(2)

            except KeyboardInterrupt:
                print(f"\n用户中断，已完成 {cycle-1} 轮")
                break
            except Exception as e:
                print(f"第 {cycle} 轮执行异常: {e}")
                continue

        print(f"\n{'='*60}")
        print(f"🎉 批量提交推送完成！")
        print(f"   总轮次: {total_cycles}")
        print(f"   成功轮次: {successful_cycles}")
        print(f"   总提交数: {total_commits}")
        print(f"   模块分配信息已保存到 module_assignment.json")
        print(f"{'='*60}")

        return True

    def configure_remote_server(self, host, password):
        """配置远程服务器信息"""
        self.remote_host = host
        self.remote_password = password
        print(f"远程服务器配置完成: {host}")

def main():
    """主函数"""
    print("Git提交记录批量生成工具")
    print("="*60)
    print(f"远程服务器: **************")
    print(f"Git仓库: http://lijiangrong@**************/r/jh_server.git")
    print(f"")
    print(f"推送策略:")
    print(f"  1. 时间渐进: 从2020年1月1日到2023年12月31日")
    print(f"  2. 每轮更改远程系统时间（按时间顺序渐进）")
    print(f"  3. 4个用户根据该时间各自提交并推送1次")
    print(f"  4. 重复步骤2-3，共执行100轮")
    print(f"  5. 总计: 400个提交 (4用户 × 100轮)")
    print(f"  6. 初始提交时间: 2020年1月1日工作时间")
    print("="*60)

    generator = GitCommitGenerator()

    # 检查ly_server目录
    if not generator.check_ly_server_directory():
        return

    # 测试远程服务器连接
    print("\n🔧 预检查阶段...")
    if not generator.test_connection():
        print("\n❌ 远程服务器连接失败，请检查:")
        print("   1. 服务器IP地址是否正确")
        print("   2. 网络连接是否正常")
        print("   3. SSH服务是否启动")
        print("   4. 用户名密码是否正确")
        return

    # 确认开始执行
    confirm = input("\n✅ 连接测试通过！确认开始生成提交记录？(y/N): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return

    # 执行批量提交
    generator.run_batch_commits(100)

if __name__ == "__main__":
    main()

